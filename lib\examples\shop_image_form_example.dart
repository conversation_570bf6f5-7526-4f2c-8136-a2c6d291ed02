import 'package:flutter/material.dart';
import '../ui/drawer_items/my_shop/widgets/shop_form_field.dart';
import '../services/shop_service.dart';

/// Example page showing how to use the new shop image form fields
/// This demonstrates the complete implementation with device access,
/// image upload, and database updating functionality.
class ShopImageFormExample extends StatefulWidget {
  final String? shopId; // Pass existing shop ID for editing, null for creation

  const ShopImageFormExample({
    super.key,
    this.shopId,
  });

  @override
  State<ShopImageFormExample> createState() => _ShopImageFormExampleState();
}

class _ShopImageFormExampleState extends State<ShopImageFormExample> {
  final _formKey = GlobalKey<FormState>();
  
  // Form data
  String _shopName = '';
  String _shopDescription = '';
  String? _shopLogoUrl;
  String? _shopBannerUrl;
  
  // Loading states
  bool _isLoading = false;
  bool _isLoadingShop = false;
  
  @override
  void initState() {
    super.initState();
    if (widget.shopId != null) {
      _loadExistingShop();
    }
  }

  Future<void> _loadExistingShop() async {
    setState(() {
      _isLoadingShop = true;
    });

    try {
      final shop = await ShopService.getShopById(widget.shopId!);
      setState(() {
        _shopName = shop.shopName;
        _shopDescription = shop.shopDescription ?? '';
        _shopLogoUrl = shop.shopLogoUrl;
        _shopBannerUrl = shop.shopBannerUrl;
      });
    } catch (e) {
      _showError('Failed to load shop: $e');
    } finally {
      setState(() {
        _isLoadingShop = false;
      });
    }
  }

  void _handleLogoChanged(String? logoUrl) {
    setState(() {
      _shopLogoUrl = logoUrl;
    });
    print('🖼️ Shop logo changed: $logoUrl');
  }

  void _handleBannerChanged(String? bannerUrl) {
    setState(() {
      _shopBannerUrl = bannerUrl;
    });
    print('🌄 Shop banner changed: $bannerUrl');
  }

  void _handleImageError(String error) {
    _showError(error);
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  Future<void> _saveShop() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      if (widget.shopId != null) {
        // Update existing shop
        await ShopService.updateShop(
          shopId: widget.shopId!,
          shopName: _shopName,
          shopDescription: _shopDescription,
          shopLogoUrl: _shopLogoUrl,
          shopBannerUrl: _shopBannerUrl,
        );
        _showSuccess('Shop updated successfully!');
      } else {
        // Create new shop
        final shop = await ShopService.createShop(
          shopName: _shopName,
          shopDescription: _shopDescription,
        );
        
        // Update with images if they were uploaded
        if (_shopLogoUrl != null || _shopBannerUrl != null) {
          await ShopService.updateShop(
            shopId: shop.id,
            shopLogoUrl: _shopLogoUrl,
            shopBannerUrl: _shopBannerUrl,
          );
        }
        
        _showSuccess('Shop created successfully!');
        Navigator.pop(context);
      }
    } catch (e) {
      _showError('Failed to save shop: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.shopId != null ? 'Edit Shop' : 'Create Shop'),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.transparent,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDark
                  ? [const Color(0xFF1A1A2E), const Color(0xFF16213E)]
                  : [const Color(0xFF667eea), const Color(0xFF764ba2)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
      ),
      body: _isLoadingShop
          ? const Center(child: CircularProgressIndicator())
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                physics: const BouncingScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Shop Logo Section
                    ShopLogoFormField(
                      label: 'Shop Logo',
                      currentLogoUrl: _shopLogoUrl,
                      onLogoChanged: _handleLogoChanged,
                      onError: _handleImageError,
                      shopId: widget.shopId,
                      size: 120,
                      isEditable: true,
                    ),
                    const SizedBox(height: 32),

                    // Shop Banner Section
                    ShopBannerFormField(
                      label: 'Shop Banner',
                      currentBannerUrl: _shopBannerUrl,
                      onBannerChanged: _handleBannerChanged,
                      onError: _handleImageError,
                      shopId: widget.shopId,
                      height: 200,
                      isEditable: true,
                      overlayWidget: _shopName.isNotEmpty
                          ? Container(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.end,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    _shopName,
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold,
                                      shadows: [
                                        Shadow(
                                          offset: Offset(0, 1),
                                          blurRadius: 3,
                                          color: Colors.black54,
                                        ),
                                      ],
                                    ),
                                  ),
                                  if (_shopDescription.isNotEmpty)
                                    Text(
                                      _shopDescription,
                                      style: const TextStyle(
                                        color: Colors.white70,
                                        fontSize: 14,
                                        shadows: [
                                          Shadow(
                                            offset: Offset(0, 1),
                                            blurRadius: 3,
                                            color: Colors.black54,
                                          ),
                                        ],
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                ],
                              ),
                            )
                          : null,
                    ),
                    const SizedBox(height: 32),

                    // Shop Name Field
                    ShopFormField(
                      label: 'Shop Name',
                      hint: 'Enter your shop name',
                      value: _shopName,
                      onChanged: (value) {
                        setState(() {
                          _shopName = value;
                        });
                      },
                      prefixIcon: Icons.store,
                      isRequired: true,
                      maxLength: 100,
                    ),
                    const SizedBox(height: 24),

                    // Shop Description Field
                    ShopFormField(
                      label: 'Shop Description',
                      hint: 'Describe what your shop offers...',
                      value: _shopDescription,
                      onChanged: (value) {
                        setState(() {
                          _shopDescription = value;
                        });
                      },
                      prefixIcon: Icons.description,
                      maxLines: 4,
                      isRequired: true,
                    ),
                    const SizedBox(height: 40),

                    // Save Button
                    SizedBox(
                      width: double.infinity,
                      height: 56,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _saveShop,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF667eea),
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          elevation: 0,
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                ),
                              )
                            : Text(
                                widget.shopId != null
                                    ? 'Update Shop'
                                    : 'Create Shop',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
