import 'dart:io';
import 'package:flutter/material.dart';
import '../../../../avatar/widgets/shop_logo_widget.dart';
import '../../../../services/shop_service.dart';
import '../../../../services/auth_service.dart';

/// Shop logo form field widget that integrates with shop forms
class ShopLogoFormField extends StatefulWidget {
  final String label;
  final String? currentLogoUrl;
  final Function(String?) onLogoChanged;
  final Function(String)? onError;
  final String? shopId;
  final bool isRequired;
  final double size;
  final bool isEditable;

  const ShopLogoFormField({
    super.key,
    this.label = 'Shop Logo',
    this.currentLogoUrl,
    required this.onLogoChanged,
    this.onError,
    this.shopId,
    this.isRequired = false,
    this.size = 120,
    this.isEditable = true,
  });

  @override
  State<ShopLogoFormField> createState() => _ShopLogoFormFieldState();
}

class _ShopLogoFormFieldState extends State<ShopLogoFormField> {
  String? _currentUserId;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCurrentUser();
  }

  Future<void> _loadCurrentUser() async {
    try {
      final user = await AuthService.getCurrentUserProfile();
      if (mounted) {
        setState(() {
          _currentUserId = user?.id;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        widget.onError?.call('Failed to load user information');
      }
    }
  }

  void _handleLogoChanged(String? logoUrl) {
    widget.onLogoChanged(logoUrl);
    
    // Update database immediately if we have a shop ID
    if (logoUrl != null && widget.shopId != null) {
      _updateLogoInDatabase(logoUrl);
    }
  }

  Future<void> _updateLogoInDatabase(String logoUrl) async {
    try {
      await ShopService.updateShopLogo(
        shopId: widget.shopId!,
        logoUrl: logoUrl,
      );
      print('✅ Shop logo updated in database: $logoUrl');
    } catch (e) {
      print('❌ Failed to update shop logo in database: $e');
      widget.onError?.call('Failed to update shop logo: $e');
    }
  }

  void _handleError(String error) {
    widget.onError?.call(error);
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Row(
          children: [
            Text(
              widget.label,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: isDark ? Colors.white : Colors.grey[800],
              ),
            ),
            if (widget.isRequired)
              Text(
                ' *',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.red[600],
                ),
              ),
          ],
        ),
        const SizedBox(height: 12),

        // Logo Widget Container
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: isDark ? Colors.grey[850] : Colors.white,
            border: Border.all(
              color: isDark ? Colors.grey[600]! : Colors.grey[300]!,
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: _isLoading
              ? Center(
                  child: SizedBox(
                    height: widget.size,
                    child: const CircularProgressIndicator(),
                  ),
                )
              : _currentUserId != null
                  ? Center(
                      child: ShopLogoWidget(
                        userId: _currentUserId!,
                        shopId: widget.shopId,
                        currentLogoUrl: widget.currentLogoUrl,
                        size: widget.size,
                        isEditable: widget.isEditable,
                        onImageChanged: _handleLogoChanged,
                        onError: _handleError,
                      ),
                    )
                  : Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 48,
                            color: Colors.red[400],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Unable to load user information',
                            style: TextStyle(
                              color: Colors.red[600],
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
        ),

        // Helper text
        if (widget.isEditable)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              'Tap the logo to upload or change your shop logo',
              style: TextStyle(
                fontSize: 12,
                color: isDark ? Colors.grey[400] : Colors.grey[600],
              ),
            ),
          ),
      ],
    );
  }
}
